// ApplicationDetail 页面样式 - CSS Modules
.applicationDetailPage {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  min-height: 100vh;

  // 头部区域
  .header {
    display: flex;
    flex-direction: column;
    gap: 16px;

    // 面包屑导航区域
    .breadcrumbSection {
      .breadcrumb {
        :global(.ant-breadcrumb-link) {
          color: #6b7280;
          text-decoration: none;
          font-size: 14px;

          &:hover {
            color: #1b3770;
          }
        }

        :global(.ant-breadcrumb-separator) {
          color: #d1d5db;
        }
      }
    }

    // 标题区域
    .titleSection {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .titleLeft {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }

  // 主要内容区域
  .mainContent {
    display: flex;
    gap: 32px;
    align-items: flex-start;

    // Tab 容器
    .tabsContainer {
      flex: 1;
      .detailTabs {
        :global(.ant-tabs-nav) {
          margin: 0;
          padding: 0;
          background: transparent;
          &::before {
            border-bottom: 0;
          }

          :global(.ant-tabs-tab) {
            padding: 16px 0;
            margin-right: 32px;
            border: none;
            background: transparent;

            :global(.ant-tabs-tab-btn) {
              font-size: 18px;
              font-weight: 500;
              color: #667085;
              line-height: 24px;
            }

            &:global(.ant-tabs-tab-active) {
              :global(.ant-tabs-tab-btn) {
                color: #204184;
                font-weight: 500;
              }
            }
          }

          :global(.ant-tabs-ink-bar) {
            background: #204184;
            height: 1.5px;
          }
        }

        :global(.ant-tabs-content-holder) {
          padding: 24px 0;

          :global(.ant-tabs-content) {
            :global(.ant-tabs-tabpane) {
              // Tab 内容样式
            }
          }
        }
      }
    }

    // 文档容器
    .documentsContainer {
      flex-shrink: 0;

      .titleRight {
        .actionButtons {
          display: flex;
          gap: 20px;
          width: 298px;
          height: 56px;
          align-items: center;
          margin-bottom: 24px;

          .submitButton {
            flex: 1;
            padding: 8px 16px;
            background: #204184;
            border: none;
            border-radius: 10px;
            color: white;
            font-family: "Inter", sans-serif;
            font-size: 14px;
            font-weight: 600;
            line-height: 18px;
            letter-spacing: 0.14px;
            box-shadow: 0px 3px 4px rgba(223, 238, 255, 0.1) inset;

            &:hover {
              background: #1a3660 !important;
              color: white !important;
            }
          }

          .returnButton {
            flex: 1;
            padding: 8px 16px;
            background: white;
            border: 1px solid #5871a3;
            border-radius: 10px;
            color: #1b3770;
            font-family: "Inter", sans-serif;
            font-size: 14px;
            font-weight: 600;
            line-height: 18px;
            letter-spacing: 0.14px;

            &:hover {
              border-color: #1b3770 !important;
              color: #1b3770 !important;
            }
          }
        }
      }
    }
  }

  // 加载状态
  .loadingContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .applicationDetailPage {
    .header {
      .titleSection {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .titleLeft {
          .pageTitle {
            font-size: 20px;
          }
        }
      }
    }

    .mainContent {
      flex-direction: column;
      gap: 24px;

      .tabsContainer {
        .detailTabs {
          :global(.ant-tabs-nav) {
            :global(.ant-tabs-tab) {
              margin-right: 24px;
              padding: 12px 0;
            }
          }

          :global(.ant-tabs-content-holder) {
            padding: 16px 0;
          }
        }
      }

      .documentsContainer {
        order: -1;
      }
    }
  }
}
