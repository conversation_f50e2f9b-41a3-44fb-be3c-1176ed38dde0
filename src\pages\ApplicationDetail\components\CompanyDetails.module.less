// Company Details 组件样式
.companyDetails {
  width: 100%;

  // 左侧内容区域
  .leftContent {
    display: flex;
    flex-direction: column;
    gap: 32px;

    // Case Info 卡片
    .caseInfoCard {
      background: #ffffff;
      border: 1px solid #e4e7ec;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0px 1px 8px 0px rgba(25, 33, 61, 0.08);
      width: 100%;

      .cardContent {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }
    }

    // Labuan Company 卡片
    .labuanCompanyCard {
      background: #ffffff;
      border: 1px solid #e4e7ec;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0px 1px 8px 0px rgba(25, 33, 61, 0.08);
      width: 100%;

      .cardContent {
        display: flex;
        flex-direction: column;
        gap: 32px;
      }
    }

    // 信息行
    .infoRow {
      display: flex;
      gap: 32px;
      align-items: flex-start;
    }

    // 单个字段
    .field {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 6px;
      min-width: 0;

      .label {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #667085;
        line-height: 18px;
        letter-spacing: 0.14px;
      }

      .value {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #081021;
        line-height: 18px;
        letter-spacing: 0.14px;
      }

      // 状态值样式
      .statusValue {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #dc6803;
        line-height: 18px;
        letter-spacing: 0.14px;
      }

      // 带图标的值
      .valueWithIcon {
        display: flex;
        align-items: center;
        gap: 6px;

        .value {
          font-family: "Inter", sans-serif;
          font-size: 14px;
          font-weight: 400;
          color: #081021;
          line-height: 18px;
          letter-spacing: 0.14px;
        }

        .externalIcon {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    // 全宽字段
    .fullWidthField {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .label {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #667085;
        line-height: 18px;
        letter-spacing: 0.14px;
      }

      .value {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #081021;
        line-height: 18px;
        letter-spacing: 0.14px;
      }
    }

    // 选项部分
    .optionSection {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    // 分隔线
    .divider {
      height: 1px;
      background: #f2f4f7;
      width: 100%;
    }

    // 检查项目
    .checkItem {
      width: 200px;
      display: flex;
      align-items: center;
      gap: 8px;

      .checkbox {
        :global(.ant-checkbox) {
          .ant-checkbox-inner {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            background-color: #1b3770;
            border-color: #1b3770;

            &::after {
              border-color: white;
            }
          }

          &.ant-checkbox-checked .ant-checkbox-inner {
            background-color: #1b3770;
            border-color: #1b3770;
          }

          &:not(.ant-checkbox-checked) .ant-checkbox-inner {
            background-color: #dee3ed;
            border-color: #d0d5dd;
          }
        }
      }

      .checkLabel {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #204184;
        line-height: 18px;
        letter-spacing: 0.14px;
        text-decoration: underline;
        flex: 1;
      }

      .checkStatus {
        font-family: "Inter", sans-serif;
        font-size: 12px;
        font-weight: 500;
        color: #079455;
        line-height: 18px;
        letter-spacing: 0.12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .companyDetails {
    .leftContent {
      gap: 24px;

      .caseInfoCard,
      .labuanCompanyCard {
        padding: 16px;

        .cardContent {
          gap: 20px;

          .infoRow {
            flex-direction: column;
            gap: 16px;

            .field {
              .label {
                font-size: 12px;
              }

              .value {
                font-size: 12px;
              }
            }
          }

          .optionSection {
            gap: 8px;

            .infoRow {
              gap: 12px;
            }
          }

          .checkItem {
            width: auto;

            .checkLabel {
              font-size: 12px;
            }

            .checkStatus {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}
