// 根据Figma设计的主布局样式 - CSS Modules
.layoutContainer {
  display: flex;
  flex-direction: row;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

// 侧边导航
.sideNavigation {
  width: 260px;
  height: 100vh; // 使用100vh而不是固定高度
  background-color: #102142; // Brand Blue/800
  display: flex;
  flex-direction: column;
  justify-content: space-between; // 确保内容和Logout按钮分布在两端
  padding: 24px; // spacing-3xl
  gap: 10px; // spacing-2.5
  overflow: hidden;
  flex-shrink: 0;

  .sideContent {
    display: flex;
    flex-direction: column;
    flex-grow: 0; // 不占用额外空间
    gap: 40px; // spacing-10
    align-items: center;
    width: 100%;
    min-height: 0; // 确保内容不会溢出

    .logoWrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      .logo {
        width: 83.333px;
        height: 45px;
        object-fit: cover;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }
    }

    .navigationMenu {
      display: flex;
      flex-direction: column;
      gap: 4px; // spacing-1
      width: 100%;
      flex-shrink: 0; // 防止菜单被压缩
    }
  }

  // Logout按钮包装器
  .logoutWrapper {
    width: 100%;
    flex-shrink: 0; // 确保不被压缩
    margin-top: auto; // 推到底部
  }
}

// 导航项
.navigationItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 12px; // spacing-2.5 spacing-3
  border-radius: 10px; // radius-lg
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.active {
    background-color: #1b3770; // Brand Blue/600
  }

  .navigationContent {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px; // spacing-2.5
    flex-grow: 1;

    .iconWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      overflow: hidden;

      .icon {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .navigationText {
      font-family: "Inter", sans-serif;
      font-weight: 500; // medium
      font-size: 16px; // text-md
      line-height: 20px; // line-height/text-sm
      color: #ffffff; // Foreground/fg-white
      letter-spacing: 0.16px;
      white-space: nowrap;
    }
  }
}

// 主内容区域
.mainContent {
  flex-grow: 1;
  background-color: #fcfcfe;
  height: 100vh; // 使用100vh与侧边导航一致
  overflow-y: auto;
  position: relative;
  min-width: 0;

  .header {
    border-bottom: 1px solid #a97c50; // Brand Brown/500
    background-color: #fff; // Brand Blue/800

    .headerContent {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 32px 64px 16px; // pt-8 pb-4 px-16
      gap: 48px; // spacing-12

      .pageTitle {
        font-family: "Inter", sans-serif;
        font-weight: 600; // semibold
        font-size: 24px; // display-xs
        line-height: 32px; // line-height/display-xs
        color: #081021; // Text/text-primary
        letter-spacing: 0.24px;
        margin: 0;
        flex-grow: 1;
        min-width: 0;
      }

      .headerActions {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12px; // spacing-3
        flex-shrink: 0;
      }
    }
  }

  .pageContent {
    padding-top: 34px;
    height: 100%;
    padding-left: 64px;
    padding-right: 64px;
    padding-bottom: 32px;
  }
}

// 用户头像
.profile {
  width: 32px;
  height: 32px;
  background-color: #d2dbff;
  border-radius: 9999px; // radius-full
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;

  .profileText {
    font-family: "Inter", sans-serif;
    font-weight: 600; // semibold
    font-size: 14px; // text-sm
    line-height: 18px; // line-height/text-xs
    color: #4469ff;
    letter-spacing: 0.14px;
    text-align: center;
  }
}

// 语言切换按钮
.languageButton {
  background-color: #ffffff; // Button/button-secondary-color-bg
  border: 1px solid #5871a3; // Button/button-secondary-color-border
  border-radius: 10px; // radius-lg
  padding: 8px 16px; // spacing-2 spacing-4
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px; // spacing-2
  width: 112px; // w-28
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: #4a5568;
  }

  span {
    font-family: "Inter", sans-serif;
    font-weight: 600; // semibold
    font-size: 14px; // text-sm
    line-height: 18px; // line-height/text-xs
    color: #1b3770; // Button/button-secondary-color-fg
    letter-spacing: 0.14px;
    flex-grow: 1;
    text-align: left;
  }

  .arrowIcon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .layoutContainer {
    flex-direction: column;
  }

  .sideNavigation {
    width: 100%;
    height: auto;
    flex-direction: row;
    padding: 16px;

    .sideContent {
      flex-direction: row;
      gap: 24px;

      .logoWrapper {
        .logo {
          width: 60px;
          height: 32px;
        }
      }

      .navigationMenu {
        flex-direction: row;
        gap: 8px;
      }
    }
  }

  .mainContent {
    .header {
      .headerContent {
        padding: 16px 24px;
        gap: 16px;

        .pageTitle {
          font-size: 20px;
          line-height: 28px;
        }
      }
    }

    .pageContent {
      padding-top: 80px;
      padding-left: 24px;
      padding-right: 24px;
    }
  }
}

@media (max-width: 768px) {
  .sideNavigation {
    padding: 12px;

    .sideContent {
      gap: 16px;

      .navigationMenu {
        .navigationItem {
          .navigationContent {
            .navigationText {
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .mainContent {
    .header {
      .headerContent {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .headerActions {
          width: 100%;
          justify-content: space-between;
        }
      }
    }

    .pageContent {
      padding-top: 120px;
      padding-left: 16px;
      padding-right: 16px;
    }
  }
}
