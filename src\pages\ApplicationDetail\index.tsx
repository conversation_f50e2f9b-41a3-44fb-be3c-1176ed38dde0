import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Spin, App, Breadcrumb, <PERSON>, Ta<PERSON>, But<PERSON> } from "antd";
import { HomeOutlined } from "@ant-design/icons";
import { getApplicationById } from "@/services/application";
import type { Application } from "@/types/application";
import { STATUS_CONFIG } from "@/types/application";
import CompanyDetails from "./components/CompanyDetails";
import DirectorsShareholdersDetails from "./components/DirectorsShareholdersDetails";
import DocumentsPanel from "./components/DocumentsPanel";
import BackToList from "@/components/BackToList";
import styles from "./index.module.less";

const ApplicationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const [application, setApplication] = useState<Application | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("company");

  // 加载应用详情
  const loadApplicationDetail = async () => {
    if (!id) {
      message.error("应用ID不存在");
      navigate("/applications");
      return;
    }

    setLoading(true);
    try {
      const data = await getApplicationById(id);
      if (data) {
        setApplication(data);
      } else {
        message.error("应用不存在");
        navigate("/applications");
      }
    } catch (error) {
      console.error("Failed to load application detail:", error);
      message.error("加载应用详情失败");
      navigate("/applications");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApplicationDetail();
  }, [id]);

  const handleSubmit = () => {
    console.log("Submit application:", application?.id);
    message.success("Application submitted successfully");
  };

  const handleReturn = () => {
    console.log("Return application:", application?.id);
    message.success("Application returned successfully");
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
    );
  }

  if (!application) {
    return null;
  }

  // Tab 项配置
  const tabItems = [
    {
      key: "company",
      label: "Company Details",
      children: <CompanyDetails application={application} />,
    },
    {
      key: "directors",
      label: "Directors/Shareholders Details",
      children: <DirectorsShareholdersDetails application={application} />,
    },
  ];

  return (
    <div className={styles.applicationDetailPage}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <div className={styles.titleLeft}>
            <BackToList />
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className={styles.mainContent}>
        {/* Tab 内容区域 */}
        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            className={styles.detailTabs}
          />
        </div>

        {/* 文档面板 */}
        <div className={styles.documentsContainer}>
          <div className={styles.titleRight}>
            <div className={styles.actionButtons}>
              <Button
                type="primary"
                className={styles.submitButton}
                onClick={handleSubmit}
              >
                Submit
              </Button>
              <Button className={styles.returnButton} onClick={handleReturn}>
                Return
              </Button>
            </div>
          </div>
          <DocumentsPanel application={application} />
        </div>
      </div>
    </div>
  );
};

export default ApplicationDetail;
