// 根据Figma设计的登录页面样式 - CSS Modules
.loginContainer {
  min-height: 100vh;
  background: var(--Brand-Blue-700, #183163);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 56px; // spacing-5xl spacing-7xl
  position: relative; // 确保不影响 message 定位
  overflow: visible; // 确保 message 可见

  .loginCard {
    background-color: #f2ebe5;
    border-radius: 10px; // radius-lg
    padding: 40px 56px; // spacing-5xl spacing-7xl
    width: 100%;
    max-width: 557px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 64px; // spacing-7xl

    .loginLogo {
      display: flex;
      justify-content: center;
      align-items: center;

      .logoImage {
        width: 146.296px;
        height: 79px;
        object-fit: cover;
      }
    }

    .loginFormWrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 64px; // spacing-7xl

      .loginForm {
        display: flex;
        flex-direction: column;
        gap: 24px; // spacing-3xl

        .formItem {
          margin-bottom: 0;

          :global(.ant-form-item-label) {
            padding-bottom: 6px; // spacing-sm

            > label {
              font-family: "Inter", sans-serif;
              font-weight: 500; // medium
              font-size: 14px; // text-sm
              line-height: 18px; // line-height/text-xs
              color: #081021; // Text/text-primary
              letter-spacing: 0.14px;
            }
          }

          .inputField {
            background-color: #ffffff; // Background/bg-primary
            border: 1px solid #d0d5dd; // Border/border-primary
            border-radius: 8px; // radius-md
            padding: 12px; // spacing-lg
            font-family: "Inter", sans-serif;
            font-weight: 400; // regular
            font-size: 16px; // text-md
            line-height: 20px; // line-height/text-sm
            color: #667085; // Text/text-placeholder
            letter-spacing: 0.16px;
            box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05);

            &::placeholder {
              color: #667085; // Text/text-placeholder
            }

            &:hover {
              border-color: #d0d5dd;
            }

            &:focus {
              border-color: #204184;
              box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05),
                0 0 0 2px rgba(32, 65, 132, 0.1);
            }
          }

          :global(.ant-input-password) {
            :global(.ant-input) {
              background-color: #ffffff;
              border: none;
              padding: 0;
              font-family: "Inter", sans-serif;
              font-weight: 400;
              font-size: 16px;
              line-height: 20px;
              color: #667085;
              letter-spacing: 0.16px;

              &::placeholder {
                color: #667085;
              }
            }

            :global(.ant-input-suffix) {
              color: #667085;
            }
          }
        }

        .formItemButton {
          margin-bottom: 0;

          .loginButton {
            background-color: #204184; // Button/button-primary-bg
            border: none;
            border-radius: 10px; // radius-lg
            padding: 12px 20px; // spacing-lg spacing-2xl
            font-family: "Inter", sans-serif;
            font-weight: 600; // semibold
            font-size: 16px; // text-md
            line-height: 20px; // line-height/text-sm
            color: #ffffff; // Button/button-primary-fg
            letter-spacing: 0.16px;
            box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25);
            height: auto;

            &:hover {
              background-color: #1a3670;
              box-shadow: 0px 4px 8px 0px rgba(32, 65, 132, 0.35);
            }

            &:focus {
              background-color: #204184;
              box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25),
                0 0 0 2px rgba(32, 65, 132, 0.2);
            }

            &:active {
              background-color: #153059;
            }
          }
        }
      }
    }
  }

  // Message 组件在登录页面的样式优化
  :global(.ant-message) {
    z-index: 999999 !important;
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;

    :global(.ant-message-notice) {
      :global(.ant-message-notice-content) {
        background: #fff !important;
        border-radius: 8px !important;
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
          0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
        padding: 12px 16px !important;

        :global(.ant-message-custom-content) {
          font-family: "Inter", sans-serif !important;
          font-size: 14px !important;
          line-height: 20px !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .loginContainer {
    padding: 20px;

    .loginCard {
      padding: 24px;
      gap: 40px;

      .loginFormWrapper {
        gap: 40px;

        .loginForm {
          gap: 20px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .loginContainer {
    padding: 16px;

    .loginCard {
      padding: 20px;
      gap: 32px;

      .loginLogo {
        .logoImage {
          width: 120px;
          height: 65px;
        }
      }

      .loginFormWrapper {
        gap: 32px;

        .loginForm {
          gap: 16px;
        }
      }
    }
  }
}
