<div style={{width: 298, paddingLeft: 16, paddingRight: 16, paddingTop: 20, paddingBottom: 20, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-secondary, #E4E7EC) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 20, display: 'inline-flex'}}>
  <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 20, display: 'inline-flex'}}>
    <div style={{flex: '1 1 0', color: 'var(--Text-text-secondary-(700), #344054)', fontSize: 18, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.18, wordWrap: 'break-word'}}>Documents</div>
    <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="IconButton" style={{padding: 4, borderRadius: 6, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
      <div style={{width: 18, height: 18, position: 'relative', overflow: 'hidden'}}>
        <div style={{width: 14.63, height: 14.63, left: 1.69, top: 1.69, position: 'absolute', background: 'var(--Foreground-fg-quaternary-(500), #667085)'}} />
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 8, display: 'flex'}}>
    <div data-show-download="false" data-state="Default" style={{alignSelf: 'stretch', height: 26, paddingLeft: 6, paddingRight: 6, paddingTop: 4, paddingBottom: 4, borderRadius: 2, justifyContent: 'flex-start', alignItems: 'center', gap: 10, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', color: 'var(--Text-text-secondary-(700), #344054)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>Compliance Schedule.pdf</div>
    </div>
    <div data-doc-10="false" data-doc-11="false" data-doc-12="false" data-doc-13="false" data-doc-14="false" data-doc-1="true" data-doc-2="true" data-doc-3="true" data-doc-4="true" data-doc-5="true" data-doc-6="false" data-doc-7="false" data-doc-8="false" data-doc-9="false" data-folder-1="false" data-folder-2="false" data-state="Dafault" style={{width: 258, paddingLeft: 6, paddingRight: 6, paddingTop: 4, paddingBottom: 4, borderRadius: 2, flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'flex'}}>
      <div style={{alignSelf: 'stretch', paddingTop: 4, paddingBottom: 4, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{width: 12, height: 12, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 10.75, height: 9.75, left: 0.62, top: 1.12, position: 'absolute', background: 'var(--Foreground-fg-quaternary-(500), #667085)'}} />
          </div>
          <div style={{flex: '1 1 0', color: 'var(--Text-text-secondary-(700), #344054)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>TAN WEI MING (Director,Shareholder,UBO)</div>
        </div>
        <div style={{width: 12, height: 12, position: 'relative'}} />
      </div>
    </div>
    <div data-doc-10="false" data-doc-11="false" data-doc-12="false" data-doc-13="false" data-doc-14="false" data-doc-1="true" data-doc-2="false" data-doc-3="false" data-doc-4="true" data-doc-5="true" data-doc-6="false" data-doc-7="false" data-doc-8="false" data-doc-9="false" data-folder-1="false" data-folder-2="false" data-state="Dafault" style={{width: 258, paddingLeft: 6, paddingRight: 6, paddingTop: 4, paddingBottom: 4, borderRadius: 2, flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'flex'}}>
      <div style={{alignSelf: 'stretch', paddingTop: 4, paddingBottom: 4, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{width: 12, height: 12, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 10.75, height: 9.75, left: 0.62, top: 1.12, position: 'absolute', background: 'var(--Foreground-fg-quaternary-(500), #667085)'}} />
          </div>
          <div style={{flex: '1 1 0', color: 'var(--Text-text-secondary-(700), #344054)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>WONG CAI HONG (Director)</div>
        </div>
        <div style={{width: 12, height: 12, position: 'relative'}} />
      </div>
    </div>
    <div data-doc-10="false" data-doc-11="false" data-doc-12="false" data-doc-13="false" data-doc-14="false" data-doc-1="false" data-doc-2="true" data-doc-3="true" data-doc-4="true" data-doc-5="true" data-doc-6="false" data-doc-7="false" data-doc-8="false" data-doc-9="false" data-folder-1="false" data-folder-2="false" data-state="Dafault" style={{width: 258, paddingLeft: 6, paddingRight: 6, paddingTop: 4, paddingBottom: 4, borderRadius: 2, flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'flex'}}>
      <div style={{alignSelf: 'stretch', paddingTop: 4, paddingBottom: 4, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{width: 12, height: 12, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 10.75, height: 9.75, left: 0.62, top: 1.12, position: 'absolute', background: 'var(--Foreground-fg-quaternary-(500), #667085)'}} />
          </div>
          <div style={{flex: '1 1 0', color: 'var(--Text-text-secondary-(700), #344054)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>LEE LAI FA (Shareholder,UBO))</div>
        </div>
        <div style={{width: 12, height: 12, position: 'relative'}} />
      </div>
    </div>
    <div data-doc-10="true" data-doc-11="true" data-doc-12="true" data-doc-13="false" data-doc-14="false" data-doc-1="true" data-doc-2="true" data-doc-3="true" data-doc-4="true" data-doc-5="true" data-doc-6="true" data-doc-7="true" data-doc-8="true" data-doc-9="true" data-folder-1="true" data-folder-2="true" data-state="Dafault" style={{width: 258, paddingLeft: 6, paddingRight: 6, paddingTop: 4, paddingBottom: 4, borderRadius: 2, flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', display: 'flex'}}>
      <div style={{alignSelf: 'stretch', paddingTop: 4, paddingBottom: 4, justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{width: 12, height: 12, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 10.75, height: 9.75, left: 0.62, top: 1.12, position: 'absolute', background: 'var(--Foreground-fg-quaternary-(500), #667085)'}} />
          </div>
          <div style={{flex: '1 1 0', color: 'var(--Text-text-secondary-(700), #344054)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>ABC SDN.BHD (Corporate Shareholder)</div>
        </div>
        <div style={{width: 12, height: 12, position: 'relative'}} />
      </div>
    </div>
  </div>
</div>