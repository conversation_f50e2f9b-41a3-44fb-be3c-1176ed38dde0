// 创建应用弹窗样式 - CSS Modules
.createApplicationModal {
  :global(.ant-modal-content) {
    padding: 0;
    border-radius: 12px; // radius-xl
    overflow: hidden;
  }

  :global(.ant-modal-body) {
    padding: 0;
  }
}

.modalContent {
  background-color: #ffffff; // Background/bg-primary
  padding: 32px; // spacing-4xl
  display: flex;
  flex-direction: column;
  gap: 32px; // spacing-6xl

  .modalTitle {
    font-family: "Inter", sans-serif;
    font-weight: 500; // medium
    font-size: 20px; // text-xl
    line-height: 24px; // line-height/text-md
    color: #081021; // Text/text-primary
    letter-spacing: 0.2px;
    margin: 0;
  }

  .form {
    display: flex;
    flex-direction: column;
    gap: 48px; // spacing-12

    .formFields {
      display: flex;
      flex-direction: column;
      gap: 20px; // spacing-2xl

      .formItem {
        margin-bottom: 0;

        :global(.ant-form-item-label) {
          padding-bottom: 6px; // spacing-sm

          > label {
            font-family: "Inter", sans-serif;
            font-weight: 500; // medium
            font-size: 14px; // text-sm
            line-height: 18px; // line-height/text-xs
            color: #081021; // Text/text-primary
            letter-spacing: 0.14px;
          }
        }

        .select {
          width: 100%;

          :global(.ant-select-selector) {
            background-color: #ffffff; // Background/bg-primary
            border: 1px solid #d0d5dd; // Border/border-primary
            border-radius: 8px; // radius-md
            padding: 12px; // spacing-lg
            height: 46px;
            display: flex;
            align-items: center;
            box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05);

            &:hover {
              border-color: #d0d5dd;
            }

            &:focus,
            &.ant-select-focused {
              border-color: #204184;
              box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05),
                0 0 0 2px rgba(32, 65, 132, 0.1);
            }
          }

          :global(.ant-select-selection-item),
          :global(.ant-select-selection-placeholder) {
            font-family: "Inter", sans-serif;
            font-weight: 400; // regular
            font-size: 16px; // text-md
            line-height: 20px; // line-height/text-sm
            color: #667085; // Text/text-placeholder
            letter-spacing: 0.16px;
          }

          :global(.ant-select-selection-item) {
            color: #081021; // Text/text-primary
          }

          .arrowIcon {
            width: 16px;
            height: 16px;
          }
        }

        .input {
          background-color: #ffffff; // Background/bg-primary
          border: 1px solid #d0d5dd; // Border/border-primary
          border-radius: 8px; // radius-md
          padding: 12px; // spacing-lg
          font-family: "Inter", sans-serif;
          font-weight: 400; // regular
          font-size: 16px; // text-md
          line-height: 20px; // line-height/text-sm
          color: #081021; // Text/text-primary
          letter-spacing: 0.16px;
          box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05);
          height: 46px;

          &::placeholder {
            color: #667085; // Text/text-placeholder
          }

          &:hover {
            border-color: #d0d5dd;
          }

          &:focus {
            border-color: #204184;
            box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05),
              0 0 0 2px rgba(32, 65, 132, 0.1);
          }
        }
      }
    }

    .buttonWrapper {
      display: flex;
      flex-direction: row;
      gap: 20px; // spacing-2xl
      width: 100%;

      .cancelButton {
        flex: 1;
        background-color: #ffffff; // Button/button-secondary-color-bg
        border: 1px solid #5871a3; // Button/button-secondary-color-border
        border-radius: 10px; // radius-lg
        padding: 12px 20px; // spacing-lg spacing-2xl
        height: 48px;
        font-family: "Inter", sans-serif;
        font-weight: 600; // semibold
        font-size: 16px; // text-md
        line-height: 20px; // line-height/text-sm
        color: #1b3770; // Button/button-secondary-color-fg
        letter-spacing: 0.16px;

        &:hover {
          background-color: #f8f9fa;
          border-color: #4a5568;
          color: #1b3770;
        }

        &:focus {
          background-color: #ffffff;
          border-color: #5871a3;
          color: #1b3770;
        }
      }

      .createButton {
        flex: 1;
        background-color: #204184; // Button/button-primary-bg
        border: none;
        border-radius: 10px; // radius-lg
        padding: 12px 20px; // spacing-lg spacing-2xl
        height: 48px;
        font-family: "Inter", sans-serif;
        font-weight: 600; // semibold
        font-size: 16px; // text-md
        line-height: 20px; // line-height/text-sm
        color: #ffffff; // Button/button-primary-fg
        letter-spacing: 0.16px;
        box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25);

        &:hover {
          background-color: #1a3670;
          box-shadow: 0px 4px 8px 0px rgba(32, 65, 132, 0.35);
        }

        &:focus {
          background-color: #204184;
          box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25),
            0 0 0 2px rgba(32, 65, 132, 0.2);
        }

        &:active {
          background-color: #153059;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .createApplicationModal {
    :global(.ant-modal) {
      width: 90% !important;
      max-width: 500px !important;
    }
  }

  .modalContent {
    padding: 24px;
    gap: 24px;

    .form {
      gap: 32px;

      .formFields {
        gap: 16px;
      }

      .buttonWrapper {
        flex-direction: column;
        gap: 12px;

        .cancelButton,
        .createButton {
          height: 44px;
        }
      }
    }
  }
}
