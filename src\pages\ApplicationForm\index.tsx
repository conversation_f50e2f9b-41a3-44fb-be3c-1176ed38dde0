import React from "react";
import { useParams } from "react-router-dom";
import { <PERSON>, Button, Result } from "antd";
import { FileTextOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import styles from "./index.module.less";

const ApplicationForm: React.FC = () => {
  const { applicationId } = useParams<{ applicationId: string }>();

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className={styles.applicationFormPage}>
      <div className={styles.container}>
        <Card className={styles.formCard}>
          <Result
            icon={<FileTextOutlined className={styles.resultIcon} />}
            title="Application Form"
            subTitle={`Application ID: ${applicationId || "Unknown"}`}
            extra={[
              <div key="info" className={styles.infoText}>
                <p>This is a placeholder page for the application form.</p>
                <p>The actual form implementation will be added here.</p>
              </div>,
              <Button 
                key="back" 
                type="primary" 
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                className={styles.backButton}
              >
                Go Back
              </Button>
            ]}
          />
        </Card>
      </div>
    </div>
  );
};

export default ApplicationForm;
