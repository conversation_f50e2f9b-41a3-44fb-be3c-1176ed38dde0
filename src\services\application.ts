// 应用相关的API服务

import type {
  Application,
  CreateApplicationRequest,
  CasePersonInCharge,
  FilterOptions,
} from "@/types/application";

// 模拟案件负责人数据
const mockCasePersons: CasePersonInCharge[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Corporate Services",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Legal Affairs",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Business Registration",
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Compliance",
  },
  {
    id: "5",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Corporate Services",
  },
];

// 模拟应用数据
const mockApplications: Application[] = [
  {
    id: "APP001",
    registrationType: "Malaysia Company",
    applicantName: "<PERSON>",
    applicantEmail: "<EMAIL>",
    applicantMobile: "+***********",
    casePersonInCharge: "<PERSON>",
    status: "Pending Review",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-16T14:20:00Z",
    companyName: "Tech Solutions Sdn Bhd",
    businessNature: "Information Technology Services",
    authorizedCapital: "RM 100,000",
    paidUpCapital: "RM 50,000",
    directors: [
      {
        id: "DIR001",
        name: "John Smith",
        nationality: "Malaysian",
        icNumber: "123456-78-9012",
        address: "123 Jalan Bukit Bintang, 55100 Kuala Lumpur",
        phoneNumber: "+***********",
        email: "<EMAIL>",
        appointmentDate: "2024-01-15",
      },
      {
        id: "DIR002",
        name: "Wong Cai Hong",
        nationality: "Malaysian",
        icNumber: "987654-32-1098",
        address: "456 Jalan Ampang, 50450 Kuala Lumpur",
        phoneNumber: "+***********",
        email: "<EMAIL>",
        appointmentDate: "2024-01-15",
      },
    ],
    shareholders: [
      {
        id: "SH001",
        name: "John Smith",
        type: "Individual",
        nationality: "Malaysian",
        icNumber: "123456-78-9012",
        address: "123 Jalan Bukit Bintang, 55100 Kuala Lumpur",
        sharePercentage: 60,
        shareAmount: "RM 30,000",
      },
      {
        id: "SH002",
        name: "Lee Lai Fa",
        type: "Individual",
        nationality: "Malaysian",
        icNumber: "111222-33-4455",
        address: "789 Jalan Raja Chulan, 50200 Kuala Lumpur",
        sharePercentage: 30,
        shareAmount: "RM 15,000",
      },
      {
        id: "SH003",
        name: "ABC Sdn Bhd",
        type: "Corporate",
        registrationNumber: "123456-A",
        address: "101 Jalan Tun Razak, 50400 Kuala Lumpur",
        sharePercentage: 10,
        shareAmount: "RM 5,000",
      },
    ],
    documents: [
      {
        id: "DOC001",
        name: "Memorandum and Articles of Association",
        type: "Legal Document",
        uploadDate: "2024-01-15",
        size: "2.5 MB",
        status: "Uploaded",
      },
      {
        id: "DOC002",
        name: "Director's IC Copy - John Smith",
        type: "Identity Document",
        uploadDate: "2024-01-15",
        size: "1.2 MB",
        status: "Uploaded",
      },
      {
        id: "DOC003",
        name: "Director's IC Copy - Wong Cai Hong",
        type: "Identity Document",
        uploadDate: "2024-01-15",
        size: "1.1 MB",
        status: "Uploaded",
      },
    ],
  },
  {
    id: "APP002",
    registrationType: "Labuan Company",
    applicantName: "Sarah Johnson",
    applicantEmail: "<EMAIL>",
    applicantMobile: "+***********",
    casePersonInCharge: "Bob Chen",
    status: "Pending Approval",
    createdAt: "2024-01-14T09:15:00Z",
    updatedAt: "2024-01-17T11:45:00Z",
    companyName: "Global Trading Ltd",
    businessNature: "International Trading and Investment",
    authorizedCapital: "USD 100,000",
    paidUpCapital: "USD 50,000",
    directors: [
      {
        id: "DIR003",
        name: "Sarah Johnson",
        nationality: "British",
        icNumber: "P123456789",
        address: "789 Marina Bay, Labuan 87000",
        phoneNumber: "+***********",
        email: "<EMAIL>",
        appointmentDate: "2024-01-14",
      },
    ],
    shareholders: [
      {
        id: "SH004",
        name: "Sarah Johnson",
        type: "Individual",
        nationality: "British",
        icNumber: "P123456789",
        address: "789 Marina Bay, Labuan 87000",
        sharePercentage: 100,
        shareAmount: "USD 50,000",
      },
    ],
    documents: [
      {
        id: "DOC004",
        name: "Memorandum and Articles of Association",
        type: "Legal Document",
        uploadDate: "2024-01-14",
        size: "2.8 MB",
        status: "Uploaded",
      },
      {
        id: "DOC005",
        name: "Director's Passport Copy - Sarah Johnson",
        type: "Identity Document",
        uploadDate: "2024-01-14",
        size: "1.5 MB",
        status: "Uploaded",
      },
    ],
  },
  {
    id: "APP003",
    registrationType: "Malaysia Company",
    applicantName: "Michael Brown",
    applicantEmail: "<EMAIL>",
    applicantMobile: "+60187654321",
    casePersonInCharge: "Carol Lim",
    status: "Completed",
    createdAt: "2024-01-10T16:00:00Z",
    updatedAt: "2024-01-18T10:30:00Z",
  },
  {
    id: "APP004",
    registrationType: "Labuan Company",
    applicantName: "Lisa Davis",
    applicantEmail: "<EMAIL>",
    applicantMobile: "+60176543210",
    casePersonInCharge: "David Tan",
    status: "Pending Started",
    createdAt: "2024-01-12T13:45:00Z",
    updatedAt: "2024-01-12T13:45:00Z",
  },
  {
    id: "APP005",
    registrationType: "Malaysia Company",
    applicantName: "Robert Wilson",
    applicantEmail: "<EMAIL>",
    applicantMobile: "+60165432109",
    casePersonInCharge: "Emma Lee",
    status: "Pending Feedback",
    createdAt: "2024-01-08T11:20:00Z",
    updatedAt: "2024-01-19T15:10:00Z",
  },
];

// 获取应用列表
export const getApplications = async (
  filters?: FilterOptions
): Promise<Application[]> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  let filteredApplications = [...mockApplications];

  if (filters) {
    if (filters.type && filters.type !== "All Type") {
      filteredApplications = filteredApplications.filter(
        (app) => app.registrationType === filters.type
      );
    }

    if (filters.status && filters.status !== "All Status") {
      filteredApplications = filteredApplications.filter(
        (app) => app.status === filters.status
      );
    }
  }

  return filteredApplications;
};

// 获取案件负责人列表
export const getCasePersons = async (): Promise<CasePersonInCharge[]> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  return mockCasePersons;
};

// 创建新应用
export const createApplication = async (
  data: CreateApplicationRequest
): Promise<Application> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 800));

  // 查找负责人姓名
  const casePerson = mockCasePersons.find(
    (person) => person.id === data.casePersonInChargeId
  );

  // 生成唯一的应用ID
  const applicationId = `APP${String(mockApplications.length + 1).padStart(
    3,
    "0"
  )}`;

  const newApplication: Application = {
    id: applicationId,
    registrationType: data.registrationType,
    applicantName: data.applicantName,
    applicantEmail: data.applicantEmail,
    applicantMobile: data.applicantMobile,
    casePersonInCharge: casePerson?.name || "Unknown",
    status: "Pending Started",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // 添加到模拟数据中
  mockApplications.unshift(newApplication);

  return newApplication;
};

// 获取应用详情
export const getApplicationById = async (
  id: string
): Promise<Application | null> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  const application = mockApplications.find((app) => app.id === id);
  return application || null;
};
