import axios, {
  type AxiosRequestConfig,
  type AxiosResponse,
  type AxiosError,
  type InternalAxiosRequestConfig,
} from "axios";
import { message } from "antd";

// 请求计数器，用于控制loading状态
let requestCount = 0;
let loadingInstance: any = null;

// 显示loading
const showLoading = () => {
  if (requestCount === 0) {
    loadingInstance = message.loading("加载中...", 0);
  }
  requestCount++;
};

// 隐藏loading
const hideLoading = () => {
  requestCount--;
  if (requestCount <= 0) {
    requestCount = 0;
    if (loadingInstance) {
      loadingInstance();
      loadingInstance = null;
    }
  }
};

// 创建axios实例
const request = axios.create({
  baseURL: "/api", // 基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 显示loading
    showLoading();

    // 可以在这里添加token等认证信息
    const token = localStorage.getItem("token");
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: AxiosError) => {
    hideLoading();
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    hideLoading();

    // 统一处理响应数据
    const { data } = response;

    // 如果后端返回的数据结构是 { code, data, message }
    if (data.code !== undefined) {
      if (data.code === 200 || data.code === 0) {
        return data.data || data;
      } else {
        message.error(data.message || "请求失败");
        return Promise.reject(new Error(data.message || "请求失败"));
      }
    }

    return data;
  },
  (error: AxiosError) => {
    hideLoading();

    // 统一错误处理
    let errorMessage = "网络错误，请稍后重试";

    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 400:
          errorMessage = "请求参数错误";
          break;
        case 401:
          errorMessage = "未授权，请重新登录";
          // 可以在这里处理登录过期，跳转到登录页
          localStorage.removeItem("token");
          window.location.href = "/login";
          break;
        case 403:
          errorMessage = "拒绝访问";
          break;
        case 404:
          errorMessage = "请求地址不存在";
          break;
        case 500:
          errorMessage = "服务器内部错误";
          break;
        default:
          errorMessage = (data as any)?.message || `请求失败 (${status})`;
      }
    } else if (error.request) {
      errorMessage = "网络连接失败，请检查网络";
    }

    message.error(errorMessage);
    return Promise.reject(error);
  }
);

export default request;

// 导出常用的请求方法
export const get = (url: string, config?: AxiosRequestConfig) => {
  return request.get(url, config);
};

export const post = (url: string, data?: any, config?: AxiosRequestConfig) => {
  return request.post(url, data, config);
};

export const put = (url: string, data?: any, config?: AxiosRequestConfig) => {
  return request.put(url, data, config);
};

export const del = (url: string, config?: AxiosRequestConfig) => {
  return request.delete(url, config);
};

export const patch = (url: string, data?: any, config?: AxiosRequestConfig) => {
  return request.patch(url, data, config);
};
