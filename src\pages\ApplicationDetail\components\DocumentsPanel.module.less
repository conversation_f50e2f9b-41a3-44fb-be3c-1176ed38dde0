// Documents Panel 组件样式
.documentsPanel {
  width: 298px;
  padding: 20px 16px;
  background: #ffffff;
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  // 标题栏
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;

    .title {
      flex: 1;
      font-family: "Inter", sans-serif;
      font-size: 18px;
      font-weight: 500;
      color: #344054;
      line-height: 24px;
      letter-spacing: 0.18px;
    }

    .downloadAllButton {
      padding: 4px;
      border: none;
      background: transparent;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;
      height: 26px;

      &:hover {
        background: #f2f4f7;
      }

      :global(.anticon) {
        font-size: 18px;
        color: #667085;
      }
    }
  }

  // 文档列表
  .documentsList {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .documentItem {
      padding: 4px 6px;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      min-height: 26px;

      &:hover {
        background: #f9fafb;
      }

      .documentInfo {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 6px;
        min-width: 0;

        .documentIcon {
          width: 12px;
          height: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .documentName {
          flex: 1;
          font-family: "Inter", sans-serif;
          font-size: 12px;
          font-weight: 400;
          color: #344054;
          line-height: 18px;
          letter-spacing: 0.12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .downloadButton {
        width: 12px;
        height: 12px;
        border: none;
        background: transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        padding: 0;

        &:hover {
          opacity: 0.7;
        }

        :global(.anticon) {
          font-size: 12px;
          color: #667085;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .documentsPanel {
    width: 100%;
    margin-top: 24px;
  }
}
