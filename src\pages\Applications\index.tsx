import React, { useState, useEffect } from "react";
import { Select, Button, Table, Tag, App } from "antd";
import { EyeOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { getApplications } from "@/services/application";
import type { Application, FilterOptions } from "@/types/application";
import {
  STATUS_CONFIG,
  TYPE_OPTIONS,
  STATUS_OPTIONS,
} from "@/types/application";
import CreateApplicationModal from "./CreateApplicationModal";
import ApplicationSuccessModal from "./ApplicationSuccessModal";
import TailwindTest from "@/components/TailwindTest";
import styles from "./index.module.less";

// 图片资源常量
const imgArrowDown =
  "http://localhost:3845/assets/c12dfcdbf5eb1e3aea0606d64e0ff869861b291d.svg";
const imgAddIcon =
  "http://localhost:3845/assets/009485e07e875c74cff35294a1a0b354096a2487.svg";
const imgVector =
  "http://localhost:3845/assets/81364c81b3b9cbbe253447dc03db8f1eabf1c1ed.svg";

// 空状态插图组件
const EmptyIllustration: React.FC = () => {
  return (
    <div className={styles.emptyIllustration}>
      <div className={styles.illustrationContainer}>
        <img
          src={imgVector}
          alt="No Records"
          className={styles.mainIllustration}
        />
      </div>
    </div>
  );
};

// 下拉选择组件
interface DropdownProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  options: { label: string; value: string }[];
}

const Dropdown: React.FC<DropdownProps> = ({
  placeholder,
  value,
  onChange,
  options,
}) => {
  return (
    <div className={styles.dropdown}>
      <Select
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={styles.select}
        suffixIcon={
          <img
            src={imgArrowDown}
            alt="Arrow Down"
            className={styles.arrowIcon}
          />
        }
        options={options}
      />
    </div>
  );
};

// 新建请求按钮组件
interface NewRequestButtonProps {
  onClick: () => void;
  className?: string;
}

const NewRequestButton: React.FC<NewRequestButtonProps> = ({
  onClick,
  className,
}) => {
  return (
    <Button
      type="primary"
      onClick={onClick}
      className={`${styles.newRequestButton} ${className || ""}`}
    >
      <img src={imgAddIcon} alt="Add" className={styles.addIcon} />
      <span>New Request</span>
    </Button>
  );
};

const Applications: React.FC = () => {
  const [typeFilter, setTypeFilter] = useState("All Type");
  const [statusFilter, setStatusFilter] = useState("All Status");
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [createdApplicationId, setCreatedApplicationId] = useState<string>("");
  const { message } = App.useApp();
  const navigate = useNavigate();

  // 加载应用列表
  const loadApplications = async () => {
    setLoading(true);
    try {
      const filters: FilterOptions = {
        type: typeFilter,
        status: statusFilter,
      };
      const data = await getApplications(filters);
      setApplications(data);
    } catch (error) {
      console.error("Failed to load applications:", error);
      message.error("Failed to load applications");
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和筛选变化时重新加载
  useEffect(() => {
    loadApplications();
  }, [typeFilter, statusFilter]);

  const handleCreateApplication = () => {
    setCreateModalVisible(true);
  };

  const handleCreateSuccess = (applicationId: string) => {
    setCreateModalVisible(false);
    setCreatedApplicationId(applicationId);
    setSuccessModalVisible(true);
    loadApplications(); // 重新加载列表
  };

  const handleCreateCancel = () => {
    setCreateModalVisible(false);
  };

  const handleSuccessModalClose = () => {
    setSuccessModalVisible(false);
    setCreatedApplicationId("");
  };

  const handleViewApplication = (record: Application) => {
    // 跳转到应用详情页面
    navigate(`/applications/${record.id}`);
  };

  // 表格列定义
  const columns = [
    {
      title: "Application ID",
      dataIndex: "id",
      key: "id",
      width: 120,
      className: styles.tableCell,
    },
    {
      title: "Registration Type",
      dataIndex: "registrationType",
      key: "registrationType",
      width: 150,
      className: styles.tableCell,
    },
    {
      title: "Applicant Name",
      dataIndex: "applicantName",
      key: "applicantName",
      width: 150,
      className: styles.tableCell,
    },
    {
      title: "Email",
      dataIndex: "applicantEmail",
      key: "applicantEmail",
      width: 200,
      className: styles.tableCell,
    },
    {
      title: "Mobile",
      dataIndex: "applicantMobile",
      key: "applicantMobile",
      width: 130,
      className: styles.tableCell,
    },
    {
      title: "Person in Charge",
      dataIndex: "casePersonInCharge",
      key: "casePersonInCharge",
      width: 150,
      className: styles.tableCell,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      className: styles.tableCell,
      render: (status: keyof typeof STATUS_CONFIG) => {
        const config = STATUS_CONFIG[status];
        return (
          <Tag
            color={config.color}
            style={{
              backgroundColor: config.bgColor,
              color: config.color,
              border: `1px solid ${config.color}`,
              borderRadius: "6px",
              padding: "2px 8px",
              fontSize: "12px",
              fontWeight: 500,
            }}
          >
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: "Action",
      key: "action",
      width: 80,
      className: styles.tableCell,
      render: (_: any, record: Application) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewApplication(record)}
          className={styles.actionButton}
        />
      ),
    },
  ];

  return (
    <div className={styles.applicationsPage}>
      {/* Tailwind CSS 测试组件 */}
      <TailwindTest />

      {/* 顶部筛选和操作区域 */}
      <div className={styles.topSection}>
        <div className={styles.filtersSection}>
          <Dropdown
            placeholder="All Type"
            value={typeFilter}
            onChange={setTypeFilter}
            options={TYPE_OPTIONS}
          />
          <Dropdown
            placeholder="All Status"
            value={statusFilter}
            onChange={setStatusFilter}
            options={STATUS_OPTIONS}
          />
        </div>
        <NewRequestButton onClick={handleCreateApplication} />
      </div>

      {/* 应用列表或空状态 */}
      {applications.length > 0 ? (
        <div className={styles.tableContainer}>
          <Table
            columns={columns}
            dataSource={applications}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} items`,
            }}
            className={styles.applicationsTable}
            scroll={{ x: 1000 }}
          />
        </div>
      ) : (
        <div className={styles.emptyStateContainer}>
          <EmptyIllustration />
          <p className={styles.emptyStateText}>
            Don't have any active applications at the moment.
          </p>
          <NewRequestButton onClick={handleCreateApplication} />
        </div>
      )}

      {/* 创建应用弹窗 */}
      <CreateApplicationModal
        visible={createModalVisible}
        onCancel={handleCreateCancel}
        onSuccess={handleCreateSuccess}
      />

      {/* 应用创建成功弹窗 */}
      <ApplicationSuccessModal
        visible={successModalVisible}
        onClose={handleSuccessModalClose}
        applicationId={createdApplicationId}
      />
    </div>
  );
};

export default Applications;
