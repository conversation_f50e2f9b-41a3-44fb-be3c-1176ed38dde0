// 应用创建成功弹窗样式 - CSS Modules
.applicationSuccessModal {
  :global(.ant-modal-content) {
    padding: 0;
    border-radius: 12px; // radius-xl
    overflow: hidden;
  }

  :global(.ant-modal-body) {
    padding: 0;
  }
}

.modalContent {
  background-color: #ffffff; // Background/bg-primary
  padding: 32px; // spacing-4xl
  display: flex;
  flex-direction: column;
  gap: 32px; // spacing-6xl

  .contentWrapper {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
    min-height: 0;

    .linkSection {
      display: flex;
      flex-direction: column;
      gap: 20px; // spacing-2xl
      width: 100%;

      .labelWrapper {
        display: flex;
        flex-direction: row;
        gap: 4px; // spacing-xs
        align-items: flex-end;
        justify-content: flex-start;

        .label {
          font-family: "Inter", sans-serif;
          font-weight: 500; // medium
          font-size: 14px; // text-sm
          line-height: 18px; // line-height/text-xs
          color: #081021; // Text/text-primary
          letter-spacing: 0.14px;
          white-space: nowrap;
        }
      }

      .inputWrapper {
        background-color: #ffffff; // Background/bg-primary
        border-radius: 8px; // radius-md
        position: relative;
        width: 100%;

        .inputContent {
          display: flex;
          flex-direction: row;
          gap: 6px; // spacing-sm
          align-items: center;
          justify-content: flex-start;
          overflow: hidden;
          padding: 12px; // spacing-lg
          width: 100%;

          .linkText {
            flex: 1;
            display: flex;
            flex-direction: row;
            gap: 4px; // spacing-xs
            align-items: center;
            justify-content: flex-start;
            min-width: 0;

            .link {
              flex: 1;
              font-family: "Inter", sans-serif;
              font-weight: 400; // regular
              font-size: 16px; // text-md
              line-height: 20px; // line-height/text-sm
              color: #667085; // Text/text-placeholder
              letter-spacing: 0.16px;
              text-decoration: underline;
              text-decoration-style: solid;
              text-underline-position: from-font;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &:hover {
                color: #475467; // Foreground/fg-quaternary
              }

              &:visited {
                color: #667085;
              }
            }
          }

          .copyButton {
            flex-shrink: 0;
            width: 16px;
            height: 16px;
            background: transparent;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            overflow: hidden;

            &:hover {
              opacity: 0.8;
            }

            &:active {
              opacity: 0.6;
            }

            .copyIcon {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
          }
        }

        // 边框和阴影
        &::after {
          content: "";
          position: absolute;
          inset: 0;
          border: 1px solid #d0d5dd; // Border/border-primary
          border-radius: 8px; // radius-md
          box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05);
          pointer-events: none;
        }
      }
    }
  }

  .buttonWrapper {
    display: flex;
    flex-direction: row;
    gap: 20px; // spacing-2xl
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;

    .closeButton {
      flex: 1;
      background-color: #ffffff; // Button/button-secondary-color-bg
      border: 1px solid #5871a3; // Button/button-secondary-color-border
      border-radius: 10px; // radius-lg
      padding: 12px 20px; // spacing-lg spacing-2xl
      height: 48px;
      display: flex;
      flex-direction: row;
      gap: 12px; // spacing-lg
      align-items: center;
      justify-content: center;
      font-family: "Inter", sans-serif;
      font-weight: 600; // semibold
      font-size: 16px; // text-md
      line-height: 20px; // line-height/text-sm
      color: #1b3770; // Button/button-secondary-color-fg
      letter-spacing: 0.16px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
        border-color: #4a5568;
        color: #1b3770;
      }

      &:focus {
        background-color: #ffffff;
        border-color: #5871a3;
        color: #1b3770;
        box-shadow: 0 0 0 2px rgba(88, 113, 163, 0.2);
      }

      &:active {
        background-color: #f1f3f4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .applicationSuccessModal {
    :global(.ant-modal) {
      width: 90% !important;
      max-width: 500px !important;
    }
  }

  .modalContent {
    padding: 24px;
    gap: 24px;

    .contentWrapper {
      .linkSection {
        gap: 16px;

        .inputWrapper {
          .inputContent {
            padding: 10px;

            .linkText {
              .link {
                font-size: 14px;
                line-height: 18px;
              }
            }
          }
        }
      }
    }

    .buttonWrapper {
      gap: 16px;

      .closeButton {
        height: 44px;
        font-size: 14px;
      }
    }
  }
}
