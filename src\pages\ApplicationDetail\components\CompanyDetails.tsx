import React from "react";
import { Checkbox } from "antd";
import type { Application } from "@/types/application";
import styles from "./CompanyDetails.module.less";

interface CompanyDetailsProps {
  application: Application;
}

const CompanyDetails: React.FC<CompanyDetailsProps> = ({ application }) => {
  return (
    <div className={styles.companyDetails}>
      {/* 左侧内容区域 */}
      <div className={styles.leftContent}>
        {/* 第一个卡片 - Case Info */}
        <div className={styles.caseInfoCard}>
          <div className={styles.cardContent}>
            {/* 第一行 */}
            <div className={styles.infoRow}>
              <div className={styles.field}>
                <div className={styles.label}>Case ID</div>
                <div className={styles.valueWithIcon}>
                  <span className={styles.value}>{application.id}</span>
                  <div className={styles.externalIcon}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d="M6 10L10 6M10 6H7M10 6V9"
                        stroke="#98A2B3"
                        strokeWidth="1.33333"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div className={styles.field}>
                <div className={styles.label}>Case Status</div>
                <div className={styles.statusValue}>{application.status}</div>
              </div>
            </div>

            {/* 第二行 */}
            <div className={styles.infoRow}>
              <div className={styles.field}>
                <div className={styles.label}>Registration Type</div>
                <div className={styles.value}>
                  {application.registrationType}
                </div>
              </div>
              <div className={styles.field}>
                <div className={styles.label}>Person In Charge</div>
                <div className={styles.value}>
                  {application.casePersonInCharge}
                </div>
              </div>
            </div>

            {/* 第三行 */}
            <div className={styles.infoRow}>
              <div className={styles.field}>
                <div className={styles.label}>Applicant Name</div>
                <div className={styles.value}>{application.applicantName}</div>
              </div>
              <div className={styles.field}>
                <div className={styles.label}>Application Date</div>
                <div className={styles.value}>
                  {new Date(application.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>

            {/* 第四行 */}
            <div className={styles.infoRow}>
              <div className={styles.field}>
                <div className={styles.label}>Applicant Mobile Number</div>
                <div className={styles.value}>
                  {application.applicantMobile}
                </div>
              </div>
              <div className={styles.field}>
                <div className={styles.label}>Applicant Email</div>
                <div className={styles.value}>{application.applicantEmail}</div>
              </div>
            </div>
          </div>
        </div>

        {application.status === "Pending Started" ? (
          <div
            data-lefticon="false"
            data-righticon="false"
            data-size="S"
            data-state="Default"
            data-type="Alert"
            className="w-44 px-4 py-2 bg-Button-button-secondary-error-bg rounded-[10px] outline outline-1 outline-offset-[-1px] outline-Button-button-secondary-error-fg inline-flex justify-center items-center gap-2"
          >
            <div className="justify-start text-Button-button-secondary-error-fg text-sm font-semibold font-['Inter'] leading-none tracking-tight">
              Cancel Application
            </div>
          </div>
        ) : (
          <>
            <div className={styles.labuanCompanyCard}>
              <div className={styles.cardContent}>
                {/* First Option Section */}
                <div className={styles.optionSection}>
                  {/* 第一行 */}
                  <div className={styles.infoRow}>
                    <div className={styles.field}>
                      <div className={styles.label}>First Option Name</div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        First Option Foreign Name
                      </div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                  </div>

                  {/* 第二行 */}
                  <div className={styles.infoRow}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Clarification for Abbreviation
                      </div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                  </div>

                  {/* Coral system 检查 */}
                  <div className={styles.checkItem}>
                    <Checkbox checked className={styles.checkbox} />
                    <div className={styles.checkLabel}>Coral system</div>
                    <div className={styles.checkStatus}>Pass</div>
                  </div>
                </div>

                {/* 分隔线 */}
                <div className={styles.divider}></div>

                {/* Secondary Option Section */}
                <div className={styles.optionSection}>
                  {/* 第一行 */}
                  <div className={styles.infoRow}>
                    <div className={styles.field}>
                      <div className={styles.label}>Secondary Option Name</div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Secondary Option Foreign Name
                      </div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                  </div>

                  {/* 第二行 */}
                  <div className={styles.infoRow}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Clarification for Abbreviation
                      </div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                  </div>

                  {/* Coral system 检查 */}
                  <div className={styles.checkItem}>
                    <Checkbox className={styles.checkbox} />
                    <div className={styles.checkLabel}>Coral system</div>
                  </div>
                </div>

                {/* 分隔线 */}
                <div className={styles.divider}></div>

                {/* Tertiary Option Section */}
                <div className={styles.optionSection}>
                  {/* 第一行 */}
                  <div className={styles.infoRow}>
                    <div className={styles.field}>
                      <div className={styles.label}>Tertiary Option Name</div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Tertiary Option Foreign Name
                      </div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                  </div>

                  {/* 第二行 */}
                  <div className={styles.infoRow}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Clarification for Abbreviation
                      </div>
                      <div className={styles.value}>ABCD</div>
                    </div>
                  </div>

                  {/* Coral system 检查 */}
                  <div className={styles.checkItem}>
                    <Checkbox className={styles.checkbox} />
                    <div className={styles.checkLabel}>Coral system</div>
                  </div>
                </div>

                {/* 分隔线 */}
                <div className={styles.divider}></div>

                {/* 其他信息字段 */}
                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>Number of Shares</div>
                    <div className={styles.value}>1,000</div>
                  </div>
                  <div className={styles.field}>
                    <div className={styles.label}>Issue Price Per Share</div>
                    <div className={styles.value}>Pending Review</div>
                  </div>
                </div>

                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>Source of Initial Fund</div>
                    <div className={styles.value}>ABC</div>
                  </div>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Relevant Business Activities
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                </div>

                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Labuan License-Trading Activity
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                </div>

                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Describe Nature of Business in Details
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                </div>

                <div className={styles.fullWidthField}>
                  <div className={styles.label}>
                    Country(ies) in which business is primarily conducted
                  </div>
                  <div className={styles.value}>ABC</div>
                </div>

                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Number of Application of Working Permit
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Number of Application of Dependent Pass
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                </div>

                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>Expected Annual Revenue</div>
                    <div className={styles.value}>ABC</div>
                  </div>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Value of Asset (current/future)
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                </div>

                <div className={styles.infoRow}>
                  <div className={styles.field}>
                    <div className={styles.label}>
                      Expected number of employee(s)
                    </div>
                    <div className={styles.value}>ABC</div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CompanyDetails;
