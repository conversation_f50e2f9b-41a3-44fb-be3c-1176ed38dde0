// 应用相关的类型定义

export interface Application {
  id: string;
  registrationType: "Malaysia Company" | "Labuan Company";
  applicantName: string;
  applicantEmail: string;
  applicantMobile: string;
  casePersonInCharge: string;
  status: ApplicationStatus;
  createdAt: string;
  updatedAt: string;
  // 详情页面额外字段
  companyName?: string;
  businessNature?: string;
  authorizedCapital?: string;
  paidUpCapital?: string;
  directors?: DirectorInfo[];
  shareholders?: ShareholderInfo[];
  documents?: DocumentInfo[];
}

export interface DirectorInfo {
  id: string;
  name: string;
  nationality: string;
  icNumber: string;
  address: string;
  phoneNumber: string;
  email: string;
  appointmentDate: string;
}

export interface ShareholderInfo {
  id: string;
  name: string;
  type: "Individual" | "Corporate";
  nationality?: string;
  icNumber?: string;
  registrationNumber?: string;
  address: string;
  sharePercentage: number;
  shareAmount: string;
}

export interface DocumentInfo {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  size: string;
  status: "Uploaded" | "Pending" | "Approved" | "Rejected";
}

export type ApplicationStatus =
  | "Pending Started" // 注册链接已形成，客户还未开始填写
  | "Pending Submit" // 客户已点击，填写中
  | "Pending Review" // 客户已提交，秘书审查中
  | "Pending Approval" // 秘书已提交批准，管理员审查中
  | "Pending Feedback" // 管理员已批准，秘书等待政府部门
  | "Completed"; // 政府部门已批准，秘书确认注册成功

export interface CasePersonInCharge {
  id: string;
  name: string;
  email: string;
  department: string;
}

export interface CreateApplicationRequest {
  registrationType: "Malaysia Company" | "Labuan Company";
  applicantName: string;
  applicantEmail: string;
  applicantMobile: string;
  casePersonInChargeId: string;
}

// 筛选选项
export interface FilterOptions {
  type: string;
  status: string;
}

// 状态显示配置
export const STATUS_CONFIG: Record<
  ApplicationStatus,
  {
    label: string;
    color: string;
    bgColor: string;
  }
> = {
  "Pending Started": {
    label: "Pending Started",
    color: "#dc6803",
    bgColor: "#fef7ed",
  },
  "Pending Submit": {
    label: "Pending Submit",
    color: "#dc6803",
    bgColor: "#fef7ed",
  },
  "Pending Review": {
    label: "Pending Review",
    color: "#dc6803",
    bgColor: "#fef7ed",
  },
  "Pending Approval": {
    label: "Pending Approval",
    color: "#dc6803",
    bgColor: "#fef7ed",
  },
  "Pending Feedback": {
    label: "Pending Feedback",
    color: "#dc6803",
    bgColor: "#fef7ed",
  },
  Completed: {
    label: "Completed",
    color: "#079455",
    bgColor: "#f0fdf4",
  },
};

// 注册类型选项
export const REGISTRATION_TYPE_OPTIONS = [
  { label: "Malaysia Company", value: "Malaysia Company" },
  { label: "Labuan Company", value: "Labuan Company" },
];

// 状态选项
export const STATUS_OPTIONS = [
  { label: "All Status", value: "All Status" },
  { label: "Pending Started", value: "Pending Started" },
  { label: "Pending Submit", value: "Pending Submit" },
  { label: "Pending Review", value: "Pending Review" },
  { label: "Pending Approval", value: "Pending Approval" },
  { label: "Pending Feedback", value: "Pending Feedback" },
  { label: "Completed", value: "Completed" },
];

// 类型选项
export const TYPE_OPTIONS = [
  { label: "All Type", value: "All Type" },
  { label: "Malaysia Company", value: "Malaysia Company" },
  { label: "Labuan Company", value: "Labuan Company" },
];
