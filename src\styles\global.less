// 全局样式覆盖

// 全局字体设置
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

#root {
  height: 100%;
}

// 设计系统变量
:root {
  // 颜色变量
  --color-white: #ffffff;
  --color-primary-900: #081021;
  --color-brand-blue-600: #1b3770;
  --color-brand-blue-800: #102142;
  --color-bg-secondary: #f2f4f7;
  --color-bg-primary: #fcfcfe;
  --color-brand-brown-500: #a97c50;
  --color-button-secondary-fg: #1b3770;
  --color-button-secondary-bg: #ffffff;
  --color-button-secondary-border: #5871a3;
  --color-icon-fg-gray: #475467;
  --color-profile-bg: #d2dbff;
  --color-profile-text: #4469ff;

  // 字体大小
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-display-xs: 24px;

  // 行高
  --line-height-xs: 18px;
  --line-height-sm: 20px;
  --line-height-display-xs: 32px;

  // 字重
  --font-weight-medium: 500;
  --font-weight-semibold: 600;

  // 间距
  --spacing-md: 8px;
  --spacing-xl: 16px;
  --spacing-3xl: 24px;

  // 圆角
  --radius-lg: 10px;
  --radius-full: 9999px;
}

// Antd组件样式覆盖
.ant-layout {
  background: var(--color-bg-primary) !important;
}

.ant-layout-sider {
  background: var(--color-brand-blue-800) !important;
}

.ant-menu {
  background: transparent !important;

  &.ant-menu-dark {
    background: transparent !important;

    .ant-menu-item {
      color: var(--color-white) !important;

      &:hover {
        color: var(--color-white) !important;
      }

      &.ant-menu-item-selected {
        background-color: var(--color-brand-blue-600) !important;
        color: var(--color-white) !important;
      }
    }
  }
}

.ant-layout-header {
  background: var(--color-bg-primary) !important;
  padding: 0 !important;
}

.ant-btn {
  &.ant-btn-default {
    background: var(--color-button-secondary-bg);
    border-color: var(--color-button-secondary-border);
    color: var(--color-button-secondary-fg);

    &:hover {
      border-color: var(--color-profile-text) !important;
      color: var(--color-button-secondary-fg) !important;
    }
  }
}

.ant-avatar {
  background: var(--color-profile-bg) !important;
  color: var(--color-profile-text) !important;
}

.ant-dropdown {
  .ant-dropdown-menu {
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Message 组件样式确保
.ant-message {
  z-index: 99999 !important;
  position: fixed !important;
  top: 100px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  pointer-events: none !important;

  .ant-message-notice {
    pointer-events: auto !important;
    margin-bottom: 16px !important;

    .ant-message-notice-content {
      background: #fff !important;
      border-radius: 8px !important;
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
      padding: 12px 16px !important;

      .ant-message-custom-content {
        font-family: "Inter", sans-serif !important;
        font-size: 14px !important;
        line-height: 20px !important;
        color: #000 !important;

        &.ant-message-error {
          color: #ff4d4f !important;
        }

        &.ant-message-success {
          color: #52c41a !important;
        }
      }
    }
  }
}

// 页面内容边距设置
.page-content {
  padding: 34px 64px;
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// 响应式设计
@media (max-width: 768px) {
  .content-wrapper {
    padding: 16px !important;
  }

  .main-header {
    padding: 16px !important;

    .header-content {
      gap: 16px !important;

      .page-title {
        font-size: 20px !important;
      }
    }
  }

  .main-layout-sider {
    width: 200px !important;
  }
}
