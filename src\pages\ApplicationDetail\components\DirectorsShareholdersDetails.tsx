import React from "react";
import { But<PERSON>, Checkbox } from "antd";
import type {
  Application,
  DirectorInfo,
  ShareholderInfo,
} from "@/types/application";
import styles from "./DirectorsShareholdersDetails.module.less";

interface DirectorsShareholdersDetailsProps {
  application: Application;
}

const DirectorsShareholdersDetails: React.FC<
  DirectorsShareholdersDetailsProps
> = ({ application }) => {
  // 背景检查项目
  const backgroundCheckItems = [
    { name: "SentroWeb", status: "Pass" },
    { name: "MACC", status: "Pass" },
    { name: "MOHA", status: "Pass" },
    { name: "OFAC", status: "Pass" },
    { name: "OFAC", status: "Pass" },
    { name: "OFSI", status: "Pass" },
    { name: "EO13382", status: "Pass" },
    { name: "BNM", status: "Pass" },
    { name: "European Union", status: "Pass" },
    { name: "Securities Commission", status: "Pass" },
  ];

  // 渲染个人资料卡片
  const renderPersonCard = (
    person: DirectorInfo | ShareholderInfo,
    type: "director" | "shareholder"
  ) => {
    const isDirector = type === "director";
    const director = person as DirectorInfo;
    const shareholder = person as ShareholderInfo;

    return (
      <div key={person.id} className={styles.personCard}>
        <div className={styles.cardContent}>
          {/* 角色标签 */}
          <div className={styles.roleTags}>
            <div className={styles.roleTagsGroup}>
              <div className={`${styles.roleTag} ${styles.directorTag}`}>
                Director
              </div>
            </div>
            <div className={styles.roleTagsGroup}>
              <div className={`${styles.roleTag} ${styles.shareholderTag}`}>
                Shareholder
              </div>
            </div>
            <div className={`${styles.roleTag} ${styles.uboTag}`}>UBO</div>
          </div>

          {/* 基本信息 */}
          <div className={styles.basicInfo}>
            {/* 第一行 */}
            <div className={styles.infoRow}>
              <div className={styles.field}>
                <div className={styles.label}>Name</div>
                <div className={styles.value}>{person.name}</div>
              </div>
              <div className={styles.field}>
                <div className={styles.label}>NRIC/Passport Number</div>
                <div className={styles.value}>
                  {isDirector
                    ? director.icNumber
                    : shareholder.icNumber ||
                      shareholder.registrationNumber ||
                      "N/A"}
                </div>
              </div>
            </div>

            {/* 第二行 */}
            <div className={styles.infoRow}>
              <div className={styles.field}>
                <div className={styles.label}>Nationality</div>
                <div className={styles.value}>
                  {isDirector
                    ? director.nationality
                    : shareholder.nationality || "N/A"}
                </div>
              </div>
              <div className={styles.field}>
                <div className={styles.label}>Percentage of shares</div>
                <div className={styles.value}>
                  {!isDirector ? `${shareholder.sharePercentage}%` : "N/A"}
                </div>
              </div>
            </div>
          </div>

          {/* 背景检查 */}
          <div className={styles.backgroundChecks}>
            {/* 第一行检查项目 */}
            <div className={styles.checkRow}>
              {backgroundCheckItems.slice(0, 3).map((item, index) => (
                <div key={index} className={styles.checkItem}>
                  <Checkbox checked className={styles.checkbox} />
                  <div className={styles.checkLabel}>{item.name}</div>
                  <div className={styles.checkStatus}>{item.status}</div>
                </div>
              ))}
            </div>

            {/* 第二行检查项目 */}
            <div className={styles.checkRow}>
              {backgroundCheckItems.slice(3, 6).map((item, index) => (
                <div key={index + 3} className={styles.checkItem}>
                  <Checkbox checked className={styles.checkbox} />
                  <div className={styles.checkLabel}>{item.name}</div>
                  <div className={styles.checkStatus}>{item.status}</div>
                </div>
              ))}
            </div>

            {/* 第三行检查项目 */}
            <div className={styles.checkRow}>
              {backgroundCheckItems.slice(6, 8).map((item, index) => (
                <div key={index + 6} className={styles.checkItem}>
                  <Checkbox checked className={styles.checkbox} />
                  <div className={styles.checkLabel}>{item.name}</div>
                  <div className={styles.checkStatus}>{item.status}</div>
                </div>
              ))}
            </div>

            {/* 第四行检查项目 */}
            <div className={styles.checkRow}>
              {backgroundCheckItems.slice(8, 10).map((item, index) => (
                <div key={index + 8} className={styles.checkItem}>
                  <Checkbox checked className={styles.checkbox} />
                  <div className={styles.checkLabel}>{item.name}</div>
                  <div className={styles.checkStatus}>{item.status}</div>
                </div>
              ))}
            </div>
          </div>

          {/* View More 按钮 */}
          <div className={styles.viewMoreSection}>
            <Button
              className={styles.viewMoreButton}
              icon={
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M5 7.5L10 12.5L15 7.5"
                    stroke="#475467"
                    strokeWidth="1.67"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              }
            >
              View More
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.directorsShareholdersDetails}>
      {/* 左侧内容区域 */}
      <div className={styles.leftContent}>
        {/* Directors Section */}
        <div className={styles.section}>
          <div className={styles.cardsContainer}>
            {application.directors?.map((director) =>
              renderPersonCard(director, "director")
            )}
          </div>
        </div>

        {/* Shareholders Section */}
        <div className={styles.section}>
          <div className={styles.cardsContainer}>
            {application.shareholders?.map((shareholder) =>
              renderPersonCard(shareholder, "shareholder")
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectorsShareholdersDetails;
