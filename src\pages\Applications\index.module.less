// 根据Figma设计的Applications页面样式 - CSS Modules
.applicationsPage {
  display: flex;
  flex-direction: column;
  gap: 20px; // spacing-2xl
  width: 100%;

  // 顶部筛选和操作区域
  .topSection {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .filtersSection {
      display: flex;
      flex-direction: row;
      gap: 24px; // spacing-3xl
      align-items: center;
    }
  }

  // 下拉选择器
  .dropdown {
    width: 200px;

    .select {
      width: 100%;
      height: 46px; // 匹配设计高度

      :global(.ant-select-selector) {
        background-color: #ffffff; // Background/bg-primary
        border: 1px solid #d0d5dd; // Border/border-primary
        border-radius: 8px; // radius-md
        padding: 12px; // spacing-lg
        font-family: "Inter", sans-serif;
        font-weight: 400; // regular
        font-size: 16px; // text-md
        line-height: 20px; // line-height/text-sm
        color: #667085; // Text/text-placeholder
        letter-spacing: 0.16px;
        box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05);
        height: 46px;
        display: flex;
        align-items: center;

        &:hover {
          border-color: #d0d5dd;
        }

        &:focus,
        &.ant-select-focused {
          border-color: #204184;
          box-shadow: 0px 0.5px 1px 0px rgba(25, 33, 61, 0.05),
            0 0 0 2px rgba(32, 65, 132, 0.1);
        }
      }

      :global(.ant-select-selection-item) {
        font-family: "Inter", sans-serif;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #667085;
        letter-spacing: 0.16px;
      }

      .arrowIcon {
        width: 16px;
        height: 16px;
      }
    }
  }

  // 新建请求按钮
  .newRequestButton {
    background-color: #204184; // Button/button-primary-bg
    border: none;
    border-radius: 10px; // radius-lg
    padding: 12px 20px; // spacing-lg spacing-2xl
    width: 165px;
    height: 48px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 12px; // spacing-lg
    font-family: "Inter", sans-serif;
    font-weight: 600; // semibold
    font-size: 16px; // text-md
    line-height: 20px; // line-height/text-sm
    color: #ffffff; // Button/button-primary-fg
    letter-spacing: 0.16px;
    box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #1a3670;
      box-shadow: 0px 4px 8px 0px rgba(32, 65, 132, 0.35);
    }

    &:focus {
      background-color: #204184;
      box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25),
        0 0 0 2px rgba(32, 65, 132, 0.2);
    }

    &:active {
      background-color: #153059;
    }

    .addIcon {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
    }

    span {
      white-space: nowrap;
    }
  }

  // 表格容器
  .tableContainer {
    padding: 0 12px;
    background: #ffffff;
    border: 1px solid #e4e7ec; // Border/border-secondary
    border-radius: 12px; // radius-xl
    overflow: hidden;
    box-shadow: 0px 1px 8px 0px rgba(25, 33, 61, 0.08);

    .applicationsTable {
      :global(.ant-table) {
        background: transparent;

        :global(.ant-table-thead) {
          :global(.ant-table-cell) {
            background: #fff; // Background/bg-secondary
            border-bottom: 1px solid #e4e7ec; // Border/border-secondary
            font-family: "Inter", sans-serif;
            font-weight: 600; // semibold
            font-size: 12px; // text-xs
            line-height: 18px; // line-height/text-xs
            color: #344054; // Text/text-secondary
            letter-spacing: 0.12px;
            padding: 12px; // spacing-lg
            text-align: left;
            height: 62px;
          }
        }

        :global(.ant-table-tbody) {
          :global(.ant-table-row) {
            &:hover {
              background: #f9fafb; // Background/bg-secondary
            }

            :global(.ant-table-cell) {
              border-bottom: 1px solid #e4e7ec; // Border/border-secondary
              padding: 16px 12px; // spacing-xl spacing-lg
              font-family: "Inter", sans-serif;
              font-weight: 400; // regular
              font-size: 14px; // text-sm
              line-height: 18px; // line-height/text-xs
              color: #081021; // Text/text-primary
              letter-spacing: 0.14px;
              vertical-align: middle;
            }
          }
        }
      }

      .tableCell {
        // 表格单元格样式已在全局样式中定义
      }

      .actionButton {
        width: 26px;
        height: 26px;
        border-radius: 6px; // radius-sm
        display: flex;
        align-items: center;
        justify-content: center;
        color: #98a2b3; // Foreground/fg-quinary
        border: none;
        background: transparent;

        &:hover {
          background: #f2f4f7; // Background/bg-secondary
          color: #475467; // Icon/icon-fg-gray
        }

        :global(.anticon) {
          font-size: 18px;
        }
      }
    }
  }

  // 空状态容器
  .emptyStateContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px; // spacing-xl
    padding: 0;
    height: 467px;
    border: 1px solid #e4e7ec; // Border/border-secondary
    border-radius: 12px; // radius-xl
    background-color: transparent;

    .emptyIllustration {
      width: 198px;
      height: 172px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .illustrationContainer {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .mainIllustration {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }

    .emptyStateText {
      font-family: "Inter", sans-serif;
      font-weight: 600; // semibold
      font-size: 16px; // text-md
      line-height: 20px; // line-height/text-sm
      color: #475467; // Text/text-tertiary
      letter-spacing: 0.16px;
      text-align: center;
      margin: 0;
      white-space: nowrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .applicationsPage {
    .topSection {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .filtersSection {
        width: 100%;
        flex-direction: column;
        gap: 12px;

        .dropdown {
          width: 100%;
        }
      }

      .newRequestButton {
        width: 100%;
      }
    }

    .tableContainer {
      .applicationsTable {
        :global(.ant-table) {
          :global(.ant-table-thead) {
            :global(.ant-table-cell) {
              font-size: 11px;
              padding: 8px;
            }
          }

          :global(.ant-table-tbody) {
            :global(.ant-table-row) {
              :global(.ant-table-cell) {
                font-size: 12px;
                padding: 12px 8px;
              }
            }
          }
        }
      }
    }

    .emptyStateContainer {
      height: auto;
      min-height: 300px;
      padding: 24px 16px;

      .emptyIllustration {
        width: 150px;
        height: 130px;
      }

      .emptyStateText {
        font-size: 14px;
        line-height: 18px;
        white-space: normal;
        text-align: center;
        max-width: 280px;
      }

      .newRequestButton {
        width: 100%;
        height: 44px;
      }
    }
  }
}

@media (max-width: 480px) {
  .applicationsPage {
    .topSection {
      .filtersSection {
        gap: 8px;
      }
    }

    .emptyStateContainer {
      padding: 16px;
      gap: 12px;

      .emptyIllustration {
        width: 120px;
        height: 100px;
      }

      .emptyStateText {
        font-size: 13px;
        line-height: 16px;
        max-width: 240px;
      }
    }
  }
}
