import React from "react";
import { useNavigate } from "react-router-dom";
import type { BackToListProps } from "./types";
import styles from "./index.module.less";

const BackToList: React.FC<BackToListProps> = ({
  to = "/applications",
  text = "Back to List",
  onClick,
  className,
  disabled = false,
  loading = false,
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (disabled || loading) return;

    if (onClick) {
      onClick();
    } else {
      navigate(to);
    }
  };

  return (
    <button
      className={`${styles.backToList} ${disabled ? styles.disabled : ""} ${
        loading ? styles.loading : ""
      } ${className || ""}`}
      onClick={handleClick}
      disabled={disabled || loading}
    >
      <div className={styles.icon}>
        {loading ? (
          <div className={styles.spinner} />
        ) : (
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M12.5 15L7.5 10L12.5 5"
              stroke="currentColor"
              strokeWidth="1.67"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </div>
      <div className={styles.text}>{text}</div>
    </button>
  );
};

export default BackToList;
