import React from 'react';

const TailwindTest: React.FC = () => {
  return (
    <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl m-4">
      <div className="md:flex">
        <div className="md:shrink-0">
          <div className="h-48 w-full object-cover md:h-full md:w-48 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-white text-2xl font-bold">TW</span>
          </div>
        </div>
        <div className="p-8">
          <div className="uppercase tracking-wide text-sm text-indigo-500 font-semibold">
            Tailwind CSS 测试
          </div>
          <h2 className="block mt-1 text-lg leading-tight font-medium text-black">
            安装成功！
          </h2>
          <p className="mt-2 text-slate-500">
            这是一个使用 Tailwind CSS 样式的测试卡片。如果你能看到这个卡片的样式（圆角、阴影、渐变背景等），
            说明 Tailwind CSS 已经成功安装并正常工作。
          </p>
          <div className="mt-4 space-x-2">
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              主要按钮
            </button>
            <button className="bg-transparent hover:bg-blue-500 text-blue-700 font-semibold hover:text-white py-2 px-4 border border-blue-500 hover:border-transparent rounded">
              次要按钮
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TailwindTest;
